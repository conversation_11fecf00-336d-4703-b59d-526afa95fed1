package preset

import (
	"dexmarket-ws/internal/database"
	"time"
)

type Preset struct {
	ID          int64  `json:"id"`
	Name        string `json:"name"`
	SettingType string `json:"settingType"`
	Slippage    int64  `json:"slippage"`
	Priority    int64  `json:"priority"`
	Bribe       int64  `json:"bribe"`
	IsAutoFee   bool   `json:"isAutoFee"`
	MaxFee      int64  `json:"maxFee"`
	MEVMode     string `json:"mevMode"`
	RPC         string `json:"rpc"`
	CreatedAt   int64  `json:"createdAt"`
	UpdatedAt   int64  `json:"updatedAt"`
}

// NewPreset creates a new preset entity with the given parameters
func NewPreset(ID int64, Name string, SettingType string, Slippage string, Priority string, Bribe string, IsAutoFee bool, MaxFee string, MEVMode string, RPC string) *Preset {
	now := time.Now().Unix()
	return &Preset{
		ID:          ID,
		SettingType: settingType,
		Slippage:    slippage,
		Priority:    priority,
		Bribe:       bribe,
		IsAutoFee:   isAuto<PERSON>ee,
		<PERSON><PERSON>ee:      maxFee,
		MEVMode:     mevMode,
		RPC:         rpc,
		Name:        name,
		CreatedAt:   now,
		UpdatedAt:   now,
	}
}

// ToPreset converts a database model to a domain entity
func ToPreset(in database.DexmarketWsUserPreset) Preset {
	return Preset{
		ID:          in.ID,
		Name:        in.Name,
		SettingType: in.SettingType,
		Slippage:    in.Slippage,
		Priority:    in.Priority,
		Bribe:       in.Bribe,
		IsAutoFee:   in.IsAutoFee,
		MaxFee:      in.MaxFee,
		MEVMode:     in.MEVMode,
		RPC:         in.RPC,
		CreatedAt:   in.CreatedAt,
		UpdatedAt:   in.UpdatedAt,
	}
}

// ToPresetDatabase converts a domain entity to a database model
func ToPresetDatabase(in Preset) database.DexmarketWsUserPreset {
	return database.DexmarketWsUserPreset{
		ID:          in.ID,
		Name:        in.Name,
		SettingType: in.SettingType,
		Slippage:    in.Slippage,
		Priority:    in.Priority,
		Bribe:       in.Bribe,
		IsAutoFee:   in.IsAutoFee,
		MaxFee:      in.MaxFee,
		MEVMode:     in.MEVMode,
		RPC:         in.RPC,
		CreatedAt:   in.CreatedAt,
		UpdatedAt:   in.UpdatedAt,
	}
}

// GetID returns the preset's ID
func (p Preset) GetID() int64 {
	return p.ID
}

// GetName returns the preset's name
func (p Preset) GetName() string {
	return p.Name
}

// GetSettingType returns the preset's setting type
func (p Preset) GetSettingType() string {
	return p.SettingType
}

// GetSlippage returns the preset's slippage
func (p Preset) GetSlippage() int64 {
	return p.Slippage
}

// GetPriority returns the preset's priority
func (p Preset) GetPriority() int64 {
	return p.Priority
}

// GetBribe returns the preset's bribe
func (p Preset) GetBribe() int64 {
	return p.Bribe
}

// GetIsAutoFee returns the preset's auto fee setting
func (p Preset) GetIsAutoFee() bool {
	return p.IsAutoFee
}

// GetMaxFee returns the preset's max fee
func (p Preset) GetMaxFee() int64 {
	return p.MaxFee
}

// GetMEVMode returns the preset's MEV mode
func (p Preset) GetMEVMode() string {
	return p.MEVMode
}

// GetRPC returns the preset's RPC
func (p Preset) GetRPC() string {
	return p.RPC
}

// GetCreatedAt returns the preset's creation timestamp
func (p Preset) GetCreatedAt() int64 {
	return p.CreatedAt
}

// GetUpdatedAt returns the preset's last update timestamp
func (p Preset) GetUpdatedAt() int64 {
	return p.UpdatedAt
}
